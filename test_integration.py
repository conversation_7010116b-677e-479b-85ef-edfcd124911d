"""
Integration test for CoHost.AI with xTTS and streaming.

This script tests the complete integration of xTTS and streaming features.

Author: <PERSON>: MIT
"""

import os
import sys
import time

# Add the src directory to the path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)
os.chdir(os.path.dirname(__file__))

def test_imports():
    """Test that all new modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        from config import Config
        print("   ✅ Config imported")
        
        from AiManager import AiManager
        print("   ✅ AiManager imported")
        
        from xtts_manager import XTTSManager
        print("   ✅ XTTSManager imported")
        
        from streaming_coordinator import StreamingCoordinator
        print("   ✅ StreamingCoordinator imported")
        
        from VoiceAssistant import VoiceAssistant
        print("   ✅ VoiceAssistant imported")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Import failed: {e}")
        return False

def test_configuration():
    """Test that configuration loads correctly with new settings."""
    print("\n⚙️ Testing configuration...")
    
    try:
        from config import Config
        config = Config()
        
        # Check new xTTS settings
        print(f"   USE_XTTS: {config.use_xtts}")
        print(f"   XTTS_MODEL: {config.xtts_model}")
        print(f"   XTTS_LANGUAGE: {config.xtts_language}")
        print(f"   ENABLE_STREAMING: {config.enable_streaming}")
        print(f"   STREAMING_CHUNK_SIZE: {config.streaming_chunk_size}")
        print(f"   STREAMING_BUFFER_TIME: {config.streaming_buffer_time}")
        
        print("   ✅ Configuration loaded successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False

def test_voice_assistant_initialization():
    """Test that VoiceAssistant can initialize with new features."""
    print("\n🤖 Testing VoiceAssistant initialization...")
    
    try:
        from VoiceAssistant import VoiceAssistant
        from config import Config
        
        # Create config with xTTS disabled for testing (to avoid model download)
        config = Config()
        config.use_xtts = False  # Use Google Cloud TTS for this test
        config.enable_streaming = True
        
        # Initialize VoiceAssistant
        assistant = VoiceAssistant(config)
        
        # Check that managers are initialized
        assert hasattr(assistant, 'ai_manager'), "AI manager not initialized"
        assert hasattr(assistant, 'tts_manager'), "TTS manager not initialized"
        assert hasattr(assistant, 'streaming_coordinator'), "Streaming coordinator not initialized"
        
        print("   ✅ VoiceAssistant initialized successfully")
        print(f"   TTS Manager type: {type(assistant.tts_manager).__name__}")
        print(f"   Streaming enabled: {assistant.streaming_coordinator is not None}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ VoiceAssistant initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_streaming_capability():
    """Test that AI manager can stream responses."""
    print("\n🔄 Testing AI streaming capability...")
    
    try:
        from AiManager import AiManager
        from config import Config
        
        config = Config()
        ai_manager = AiManager(model=config.ollama_model)
        
        # Test streaming method exists
        assert hasattr(ai_manager, 'chat_with_history_streaming'), "Streaming method not found"
        
        print("   ✅ AI streaming method available")
        
        # Try a quick streaming test (if Ollama is available)
        try:
            test_question = "Say hello"
            chunks = list(ai_manager.chat_with_history_streaming(test_question))
            print(f"   ✅ Streaming test successful ({len(chunks)} chunks)")
            return True
        except Exception as e:
            print(f"   ⚠️ Streaming test skipped (Ollama not available): {e}")
            return True  # Still pass if Ollama isn't running
        
    except Exception as e:
        print(f"   ❌ AI streaming test failed: {e}")
        return False

def main():
    """Main integration test."""
    print("🚀 CoHost.AI Integration Test - xTTS & Streaming")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("VoiceAssistant Initialization", test_voice_assistant_initialization),
        ("AI Streaming Capability", test_ai_streaming_capability),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Integration Test Results:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 Integration test successful!")
        print("   xTTS and streaming features are properly integrated")
        print("\n📝 Next steps:")
        print("   1. Run 'python tests/test_xtts.py' to test xTTS functionality")
        print("   2. Run 'python tests/test_streaming.py' to test streaming")
        print("   3. Update your .env file with desired settings")
        print("   4. Start CoHost.AI with 'python main.py'")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed")
        print("   Please resolve issues before using new features")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
