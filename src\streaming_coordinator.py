"""
Streaming Coordinator for CoHost.AI.

This module coordinates streaming responses between the AI manager and TTS system
to minimize latency and provide smooth, natural-sounding responses.

Author: <PERSON>: MIT
"""

import logging
import threading
import time
import re
from typing import Generator, Optional, Callable
from queue import Queue, Empty

logger = logging.getLogger(__name__)


class StreamingCoordinator:
    """
    Coordinates streaming between AI responses and TTS synthesis.
    
    This class manages the flow of streaming text from the AI model to the TTS system,
    implementing intelligent buffering and sentence boundary detection to minimize
    latency while avoiding unnatural pauses in speech.
    
    Attributes:
        ai_manager: AI manager instance for generating streaming responses
        tts_manager: TTS manager instance for audio synthesis
        min_chunk_size: Minimum characters before considering TTS synthesis
        max_buffer_time: Maximum time to buffer text before forcing synthesis
        sentence_endings: Patterns that indicate sentence boundaries
        buffer_lock: Thread lock for text buffer operations
    """
    
    def __init__(self, ai_manager, tts_manager, min_chunk_size: int = 20, max_buffer_time: float = 2.0):
        """
        Initialize the streaming coordinator.
        
        Args:
            ai_manager: AI manager instance with streaming capabilities
            tts_manager: TTS manager instance (xTTS or fallback)
            min_chunk_size: Minimum characters before considering synthesis
            max_buffer_time: Maximum seconds to buffer before forcing synthesis
        """
        self.ai_manager = ai_manager
        self.tts_manager = tts_manager
        self.min_chunk_size = min_chunk_size
        self.max_buffer_time = max_buffer_time
        
        # Sentence boundary patterns
        self.sentence_endings = re.compile(r'[.!?;]\s+')
        self.buffer_lock = threading.Lock()
        
        logger.info("Streaming coordinator initialized")
    
    def process_streaming_response(self, question: str, 
                                 scene_name: str = "In-Game [OLD]",
                                 bot_source: str = "AIBot", 
                                 top_source: str = "AITop",
                                 progress_callback: Optional[Callable[[str], None]] = None) -> str:
        """
        Process a question with streaming AI response and TTS.
        
        Args:
            question: User question to process
            scene_name: OBS scene name for character visibility
            bot_source: OBS source name for bot character
            top_source: OBS source name for top character
            progress_callback: Optional callback for progress updates
            
        Returns:
            Complete AI response text
        """
        try:
            logger.info(f"Processing streaming response for: {question[:50]}...")
            
            # Get streaming response from AI
            response_stream = self.ai_manager.chat_with_history_streaming(question)
            
            # Process the stream with TTS
            complete_response = self._process_text_stream(
                response_stream, 
                scene_name, 
                bot_source, 
                top_source,
                progress_callback
            )
            
            logger.info("Streaming response processing completed")
            return complete_response
            
        except Exception as e:
            logger.error(f"Error in streaming response processing: {e}")
            # Fallback to non-streaming
            return self.ai_manager.chat_with_history(question)
    
    def _process_text_stream(self, text_stream: Generator[str, None, None],
                           scene_name: str, bot_source: str, top_source: str,
                           progress_callback: Optional[Callable[[str], None]] = None) -> str:
        """
        Process streaming text with intelligent buffering and TTS synthesis.
        
        Args:
            text_stream: Generator yielding text chunks
            scene_name: OBS scene name
            bot_source: OBS bot source name
            top_source: OBS top source name
            progress_callback: Optional callback for progress updates
            
        Returns:
            Complete response text
        """
        text_buffer = ""
        complete_response = ""
        last_synthesis_time = time.time()
        synthesis_queue = Queue()
        obs_shown = False
        
        # Start TTS worker thread
        tts_thread = threading.Thread(
            target=self._tts_worker,
            args=(synthesis_queue, scene_name, bot_source, top_source),
            daemon=True,
            name="StreamingTTS-Worker"
        )
        tts_thread.start()
        
        try:
            for chunk in text_stream:
                if not chunk:
                    continue
                    
                with self.buffer_lock:
                    text_buffer += chunk
                    complete_response += chunk
                
                # Update progress if callback provided
                if progress_callback:
                    progress_callback(chunk)
                
                # Check if we should synthesize current buffer
                current_time = time.time()
                should_synthesize = (
                    self._has_sentence_boundary(text_buffer) or
                    len(text_buffer) >= self.min_chunk_size and 
                    (current_time - last_synthesis_time) >= self.max_buffer_time
                )
                
                if should_synthesize and text_buffer.strip():
                    # Extract sentences to synthesize
                    sentences_to_synthesize, remaining_buffer = self._extract_sentences(text_buffer)
                    
                    if sentences_to_synthesize:
                        # Queue for TTS synthesis
                        synthesis_queue.put({
                            'text': sentences_to_synthesize,
                            'show_obs': not obs_shown
                        })
                        obs_shown = True
                        
                        logger.debug(f"Queued for TTS: {sentences_to_synthesize[:50]}...")
                        
                        with self.buffer_lock:
                            text_buffer = remaining_buffer
                        
                        last_synthesis_time = current_time
            
            # Process any remaining text
            if text_buffer.strip():
                synthesis_queue.put({
                    'text': text_buffer.strip(),
                    'show_obs': not obs_shown
                })
                logger.debug(f"Final TTS: {text_buffer.strip()[:50]}...")
            
            # Signal end of synthesis
            synthesis_queue.put(None)
            
            # Wait for TTS thread to complete
            tts_thread.join(timeout=30.0)
            
            return complete_response
            
        except Exception as e:
            logger.error(f"Error processing text stream: {e}")
            # Signal TTS thread to stop
            synthesis_queue.put(None)
            return complete_response
    
    def _tts_worker(self, synthesis_queue: Queue, scene_name: str, bot_source: str, top_source: str):
        """
        Worker thread for TTS synthesis.
        
        Args:
            synthesis_queue: Queue containing text to synthesize
            scene_name: OBS scene name
            bot_source: OBS bot source name
            top_source: OBS top source name
        """
        total_audio_duration = 0.0
        obs_shown = False
        
        try:
            while True:
                try:
                    item = synthesis_queue.get(timeout=5.0)
                    if item is None:  # End signal
                        break
                    
                    text = item['text']
                    show_obs = item['show_obs']
                    
                    # Show OBS sources if this is the first synthesis
                    if show_obs and not obs_shown:
                        if hasattr(self.tts_manager, 'obs_manager') and self.tts_manager.obs_manager:
                            self.tts_manager._handle_obs_visibility(scene_name, bot_source, top_source, True)
                        obs_shown = True
                    
                    # Synthesize and queue audio
                    if hasattr(self.tts_manager, 'synthesize_to_bytes'):
                        # xTTS manager
                        audio_data = self.tts_manager.synthesize_to_bytes(text)
                        self.tts_manager.audio_queue.put(audio_data)
                        total_audio_duration += self.tts_manager._estimate_audio_duration(audio_data)
                    else:
                        # Fallback to regular synthesis
                        self.tts_manager.synthesize_and_play(text, scene_name, bot_source, top_source)
                    
                    synthesis_queue.task_done()
                    
                except Empty:
                    continue
                except Exception as e:
                    logger.error(f"Error in TTS worker: {e}")
                    if not synthesis_queue.empty():
                        synthesis_queue.task_done()
            
            # Hide OBS sources after all audio completes
            if obs_shown and hasattr(self.tts_manager, 'obs_manager') and self.tts_manager.obs_manager:
                time.sleep(total_audio_duration + 0.5)
                self.tts_manager._handle_obs_visibility(scene_name, bot_source, top_source, False)
                
        except Exception as e:
            logger.error(f"TTS worker thread error: {e}")
    
    def _has_sentence_boundary(self, text: str) -> bool:
        """
        Check if text contains a sentence boundary.
        
        Args:
            text: Text to check
            
        Returns:
            True if text contains a sentence boundary
        """
        return bool(self.sentence_endings.search(text))
    
    def _extract_sentences(self, text: str) -> tuple[str, str]:
        """
        Extract complete sentences from text buffer.
        
        Args:
            text: Text buffer to process
            
        Returns:
            Tuple of (sentences_to_synthesize, remaining_buffer)
        """
        # Find the last sentence boundary
        matches = list(self.sentence_endings.finditer(text))
        
        if matches:
            # Get the position after the last sentence ending
            last_match = matches[-1]
            split_pos = last_match.end()
            
            sentences = text[:split_pos].strip()
            remaining = text[split_pos:].strip()
            
            return sentences, remaining
        else:
            # No complete sentences, check if buffer is long enough to force synthesis
            if len(text) >= self.min_chunk_size * 2:
                # Find a good break point (space, comma, etc.)
                break_points = [' ', ',', ';', ':']
                best_break = -1
                
                for i in range(len(text) - 1, self.min_chunk_size - 1, -1):
                    if text[i] in break_points:
                        best_break = i + 1
                        break
                
                if best_break > 0:
                    return text[:best_break].strip(), text[best_break:].strip()
            
            # No good break point found
            return "", text
    
    def fallback_to_regular_processing(self, question: str, scene_name: str, bot_source: str, top_source: str) -> str:
        """
        Fallback to regular non-streaming processing.
        
        Args:
            question: User question
            scene_name: OBS scene name
            bot_source: OBS bot source name
            top_source: OBS top source name
            
        Returns:
            AI response text
        """
        logger.info("Using fallback non-streaming processing")
        
        # Get regular AI response
        response = self.ai_manager.chat_with_history(question)
        
        # Synthesize with TTS
        self.tts_manager.synthesize_and_play(response, scene_name, bot_source, top_source)
        
        return response
