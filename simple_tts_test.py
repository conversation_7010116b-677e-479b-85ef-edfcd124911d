"""
Simple TTS test that avoids complex operations.
"""

def test_basic_import():
    """Test basic TTS import."""
    print("Testing basic TTS import...")
    
    try:
        from TTS.api import TTS
        print("✅ TTS imported successfully")
        return True
    except Exception as e:
        print(f"❌ TTS import failed: {e}")
        return False

def test_model_creation():
    """Test creating TTS model."""
    print("Testing TTS model creation...")
    
    try:
        from TTS.api import TTS
        import torch
        
        # Force CPU to avoid CUDA issues
        device = "cpu"
        print(f"Using device: {device}")
        
        # Create TTS instance with specific model
        print("Creating TTS instance...")
        tts = TTS(model_name="tts_models/multilingual/multi-dataset/xtts_v2", progress_bar=False)
        
        print("Moving to device...")
        tts = tts.to(device)
        
        print("✅ TTS model created successfully")
        return True
        
    except Exception as e:
        print(f"❌ TTS model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_synthesis():
    """Test basic synthesis."""
    print("Testing synthesis...")
    
    try:
        from TTS.api import TTS
        import torch
        
        device = "cpu"
        tts = TTS(model_name="tts_models/multilingual/multi-dataset/xtts_v2", progress_bar=False).to(device)
        
        # Test synthesis
        text = "Hello world"
        print(f"Synthesizing: '{text}'")
        
        wav = tts.tts(text=text, language="en")
        
        print(f"✅ Synthesis successful: {len(wav)} samples")
        return True
        
    except Exception as e:
        print(f"❌ Synthesis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_playback():
    """Test audio playback."""
    print("Testing audio playback...")
    
    try:
        from TTS.api import TTS
        import torch
        import pyaudio
        import numpy as np
        
        device = "cpu"
        tts = TTS(model_name="tts_models/multilingual/multi-dataset/xtts_v2", progress_bar=False).to(device)
        
        # Synthesize
        text = "This is a test of text to speech."
        wav = tts.tts(text=text, language="en")
        
        # Convert to int16
        if wav.dtype != np.int16:
            wav = (wav * 32767).astype(np.int16)
        
        # Play audio
        p = pyaudio.PyAudio()
        
        # Check device 7
        try:
            info = p.get_device_info_by_index(7)
            print(f"Using audio device 7: {info['name']}")
        except Exception as e:
            print(f"Device 7 not available: {e}")
            p.terminate()
            return False
        
        stream = p.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=22050,
            output=True,
            output_device_index=7
        )
        
        print("🔊 Playing audio...")
        
        # Play in chunks
        chunk_size = 4096
        for i in range(0, len(wav), chunk_size):
            chunk = wav[i:i+chunk_size]
            stream.write(chunk.tobytes())
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        print("✅ Audio playback completed")
        return True
        
    except Exception as e:
        print(f"❌ Audio playback failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Simple TTS Test")
    print("=" * 30)
    
    tests = [
        ("Basic Import", test_basic_import),
        ("Model Creation", test_model_creation),
        ("Synthesis", test_synthesis),
        ("Audio Playback", test_audio_playback),
    ]
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 20)
        
        try:
            success = test_func()
            if not success:
                print(f"❌ {test_name} failed - stopping tests")
                break
        except KeyboardInterrupt:
            print("\n⚠️ Test interrupted by user")
            break
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            break
    else:
        print("\n🎉 All tests completed successfully!")
        print("TTS should be working. If you didn't hear audio:")
        print("- Check audio device configuration")
        print("- Verify system volume")
        print("- Try a different audio device index")
