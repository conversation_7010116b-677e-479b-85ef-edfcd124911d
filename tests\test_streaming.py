"""
Test script for streaming functionality in CoHost.AI.

This script tests the streaming coordinator and AI manager streaming capabilities.

Author: <PERSON>: MIT
"""

import os
import sys
import time
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from config import Config
from AiManager import AiManager
from xtts_manager import XTTSManager
from streaming_coordinator import StreamingCoordinator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_ai_streaming() -> bool:
    """
    Test AI manager streaming functionality.
    
    Returns:
        bool: True if streaming works, False otherwise
    """
    try:
        print("🤖 Testing AI streaming...")
        
        config = Config()
        ai_manager = AiManager(model=config.ollama_model)
        
        test_question = "Tell me a short story about a robot learning to paint."
        
        print(f"   Question: {test_question}")
        print("   Streaming response:")
        
        start_time = time.time()
        response_chunks = []
        
        for chunk in ai_manager.chat_with_history_streaming(test_question):
            print(chunk, end="", flush=True)
            response_chunks.append(chunk)
        
        total_time = time.time() - start_time
        complete_response = "".join(response_chunks)
        
        print(f"\n\n✅ AI streaming completed in {total_time:.2f} seconds")
        print(f"   Total response length: {len(complete_response)} characters")
        print(f"   Chunks received: {len(response_chunks)}")
        
        return len(response_chunks) > 1  # Should have multiple chunks
        
    except Exception as e:
        print(f"❌ AI streaming test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streaming_coordinator() -> bool:
    """
    Test the streaming coordinator functionality.
    
    Returns:
        bool: True if coordinator works, False otherwise
    """
    try:
        print("\n🔄 Testing streaming coordinator...")
        
        config = Config()
        ai_manager = AiManager(model=config.ollama_model)
        
        # Use xTTS for testing
        xtts_manager = XTTSManager(
            device_index=7,
            cache_enabled=False,
            model_name="tts_models/multilingual/multi-dataset/xtts_v2",
            language="en"
        )
        
        coordinator = StreamingCoordinator(
            ai_manager=ai_manager,
            tts_manager=xtts_manager,
            min_chunk_size=15,
            max_buffer_time=1.5
        )
        
        test_question = "Explain how streaming reduces latency in AI responses."
        
        print(f"   Question: {test_question}")
        print("   Processing with streaming coordinator...")
        
        start_time = time.time()
        
        def progress_callback(chunk):
            print(f"[CHUNK] {chunk}", end="", flush=True)
        
        response = coordinator.process_streaming_response(
            test_question,
            progress_callback=progress_callback
        )
        
        total_time = time.time() - start_time
        
        print(f"\n\n✅ Streaming coordinator completed in {total_time:.2f} seconds")
        print(f"   Final response length: {len(response)} characters")
        
        # Clean up
        xtts_manager.stop()
        
        return len(response) > 0
        
    except Exception as e:
        print(f"❌ Streaming coordinator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sentence_boundary_detection() -> bool:
    """
    Test sentence boundary detection in streaming coordinator.
    
    Returns:
        bool: True if detection works correctly, False otherwise
    """
    try:
        print("\n📝 Testing sentence boundary detection...")
        
        # Create a minimal coordinator for testing
        config = Config()
        ai_manager = AiManager(model=config.ollama_model)
        
        # Mock TTS manager for testing
        class MockTTSManager:
            def synthesize_to_bytes(self, text):
                return b"mock_audio_data"
            def _estimate_audio_duration(self, audio_data):
                return 1.0
            def __init__(self):
                self.audio_queue = type('Queue', (), {'put': lambda self, x: None})()
        
        mock_tts = MockTTSManager()
        coordinator = StreamingCoordinator(ai_manager, mock_tts)
        
        # Test cases
        test_cases = [
            ("Hello world", False),  # No sentence ending
            ("Hello world.", True),   # Period
            ("Are you there?", True), # Question mark
            ("Wow!", True),          # Exclamation
            ("Let's see; maybe", True), # Semicolon
            ("Short", False),        # Too short even with ending
        ]
        
        all_passed = True
        for text, expected in test_cases:
            result = coordinator._is_sentence_complete(text)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{text}' -> {result} (expected {expected})")
            if result != expected:
                all_passed = False
        
        if all_passed:
            print("✅ Sentence boundary detection working correctly")
        else:
            print("❌ Some sentence boundary tests failed")
            
        return all_passed
        
    except Exception as e:
        print(f"❌ Sentence boundary test failed: {e}")
        return False

def test_performance_comparison() -> bool:
    """
    Compare streaming vs non-streaming performance.
    
    Returns:
        bool: True if comparison completes, False otherwise
    """
    try:
        print("\n⚡ Performance comparison: Streaming vs Non-streaming")
        
        config = Config()
        ai_manager = AiManager(model=config.ollama_model)
        
        test_question = "What are the benefits of AI in modern technology?"
        
        # Test non-streaming
        print("   Testing non-streaming response...")
        start_time = time.time()
        regular_response = ai_manager.chat_with_history(test_question)
        regular_time = time.time() - start_time
        
        # Test streaming
        print("   Testing streaming response...")
        start_time = time.time()
        streaming_chunks = []
        first_chunk_time = None
        
        for chunk in ai_manager.chat_with_history_streaming(test_question):
            if first_chunk_time is None:
                first_chunk_time = time.time() - start_time
            streaming_chunks.append(chunk)
        
        total_streaming_time = time.time() - start_time
        streaming_response = "".join(streaming_chunks)
        
        # Results
        print(f"\n📊 Performance Results:")
        print(f"   Non-streaming total time: {regular_time:.2f}s")
        print(f"   Streaming total time: {total_streaming_time:.2f}s")
        print(f"   Time to first chunk: {first_chunk_time:.2f}s")
        print(f"   Latency improvement: {((regular_time - first_chunk_time) / regular_time * 100):.1f}%")
        print(f"   Response lengths: Regular={len(regular_response)}, Streaming={len(streaming_response)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance comparison failed: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 CoHost.AI - Streaming Test Suite")
    print("=" * 60)
    
    # Check if Ollama is available
    try:
        config = Config()
        ai_manager = AiManager(model=config.ollama_model)
        # Quick test
        list(ai_manager.chat_with_history_streaming("Hi"))
        print("✅ Ollama connection verified")
    except Exception as e:
        print(f"❌ Ollama not available: {e}")
        print("   Please ensure Ollama is running with the configured model")
        return False
    
    tests = [
        ("AI Streaming", test_ai_streaming),
        ("Streaming Coordinator", test_streaming_coordinator),
        ("Sentence Boundary Detection", test_sentence_boundary_detection),
        ("Performance Comparison", test_performance_comparison),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Test: {test_name}")
        print("-" * 40)
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All streaming tests passed!")
        print("   Streaming functionality is ready for use")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("   Please resolve issues before using streaming features")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
