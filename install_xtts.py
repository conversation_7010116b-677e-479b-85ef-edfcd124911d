"""
Installation script for xTTS in CoHost.AI.

This script properly installs and configures xTTS v2 for local text-to-speech.
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"   Error: {e.stderr.strip()}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 9 or version.minor >= 12:
        print("❌ Python version not compatible with xTTS")
        print("   xTTS requires Python 3.9, 3.10, or 3.11")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print("✅ Python version is compatible")
    return True

def uninstall_old_tts():
    """Uninstall old TTS packages."""
    packages_to_remove = ["coqui-tts", "coqui-tts-trainer"]
    
    for package in packages_to_remove:
        print(f"🗑️ Removing {package}...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "uninstall", package, "-y"], 
                         check=False, capture_output=True)
            print(f"   ✅ {package} removed")
        except:
            print(f"   ℹ️ {package} was not installed")

def install_tts():
    """Install the correct TTS package."""
    print("📦 Installing TTS package...")
    
    # Install TTS with specific version
    commands = [
        f"{sys.executable} -m pip install --upgrade pip",
        f"{sys.executable} -m pip install TTS>=0.22.0",
    ]
    
    for cmd in commands:
        if not run_command(cmd, f"Running: {cmd}"):
            return False
    
    return True

def test_installation():
    """Test if TTS installation works."""
    print("🧪 Testing TTS installation...")
    
    try:
        # Test imports
        import torch
        print(f"   ✅ PyTorch: {torch.__version__}")
        
        from TTS.api import TTS
        print("   ✅ TTS imported successfully")
        
        # Test model listing
        print("   📋 Available models:")
        tts = TTS()
        models = tts.list_models()
        if "tts_models/multilingual/multi-dataset/xtts_v2" in models:
            print("   ✅ xTTS v2 model available")
        else:
            print("   ⚠️ xTTS v2 model not found in list")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False

def update_config():
    """Update configuration to use xTTS."""
    print("⚙️ Updating configuration...")
    
    env_file = ".env"
    if os.path.exists(env_file):
        # Read current config
        with open(env_file, 'r') as f:
            lines = f.readlines()
        
        # Update USE_XTTS setting
        updated = False
        for i, line in enumerate(lines):
            if line.startswith("USE_XTTS="):
                lines[i] = "USE_XTTS=true\n"
                updated = True
                break
        
        if not updated:
            lines.append("USE_XTTS=true\n")
        
        # Write back
        with open(env_file, 'w') as f:
            f.writelines(lines)
        
        print("   ✅ Configuration updated to use xTTS")
    else:
        print("   ⚠️ .env file not found, please set USE_XTTS=true manually")

def main():
    """Main installation process."""
    print("🚀 CoHost.AI xTTS Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Uninstall old packages
    uninstall_old_tts()
    
    # Install correct TTS package
    if not install_tts():
        print("\n❌ TTS installation failed")
        return False
    
    # Test installation
    if not test_installation():
        print("\n❌ TTS installation test failed")
        return False
    
    # Update config
    update_config()
    
    print("\n🎉 xTTS installation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Test xTTS: python tests/test_xtts.py")
    print("2. Test streaming: python tests/test_streaming.py")
    print("3. Run CoHost.AI: python main.py")
    print("\n💡 Tips:")
    print("- First model download may take time")
    print("- GPU acceleration requires CUDA")
    print("- Add XTTS_SPEAKER_WAV for voice cloning")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Installation failed. Please check the errors above.")
        sys.exit(1)
    else:
        print("\n✅ Installation successful!")
        sys.exit(0)
