"""
Quick xTTS test to diagnose audio issues.
"""

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_xtts():
    """Test basic xTTS functionality."""
    print("🤖 Quick xTTS Test")
    print("=" * 40)
    
    try:
        # Test imports
        print("1. Testing imports...")
        from TTS.api import TTS
        import torch
        import pyaudio
        import numpy as np
        print("   ✅ All imports successful")
        
        # Check CUDA
        print(f"2. CUDA available: {torch.cuda.is_available()}")
        
        # Test audio devices
        print("3. Testing audio devices...")
        p = pyaudio.PyAudio()
        device_count = p.get_device_count()
        print(f"   Found {device_count} audio devices")
        
        # Check device 7
        try:
            info = p.get_device_info_by_index(7)
            print(f"   Device 7: {info['name']} ({info['maxOutputChannels']} channels)")
        except Exception as e:
            print(f"   ❌ Device 7 error: {e}")
            p.terminate()
            return False
        
        p.terminate()
        
        # Test basic tone generation
        print("4. Testing basic audio playback...")
        p = pyaudio.PyAudio()
        
        # Generate a simple beep
        sample_rate = 22050
        duration = 1.0
        frequency = 440
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        tone = np.sin(2 * np.pi * frequency * t) * 0.3
        tone = (tone * 32767).astype(np.int16)
        
        stream = p.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=sample_rate,
            output=True,
            output_device_index=7,
            frames_per_buffer=4096
        )
        
        print("   🔊 Playing test beep (you should hear a tone)...")
        stream.write(tone.tobytes())
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        print("   ✅ Basic audio test completed")
        
        # Test xTTS model loading (this might take time)
        print("5. Testing xTTS model loading...")
        print("   ⏳ This may take a moment on first run...")
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device)
        print("   ✅ xTTS model loaded successfully")
        
        # Test synthesis
        print("6. Testing xTTS synthesis...")
        test_text = "Hello! This is a test of xTTS synthesis."
        print(f"   Synthesizing: '{test_text}'")
        
        wav = tts.tts(text=test_text, language="en")
        print(f"   ✅ Synthesis completed: {len(wav)} samples")
        
        # Convert and play
        print("7. Testing xTTS audio playback...")
        if isinstance(wav, np.ndarray):
            if wav.dtype != np.int16:
                wav = (wav * 32767).astype(np.int16)
        
        p = pyaudio.PyAudio()
        stream = p.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=22050,
            output=True,
            output_device_index=7,
            frames_per_buffer=4096
        )
        
        print("   🔊 Playing xTTS audio (you should hear speech)...")
        
        # Play in chunks
        chunk_size = 4096
        for i in range(0, len(wav), chunk_size):
            chunk = wav[i:i+chunk_size]
            stream.write(chunk.tobytes())
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        print("   ✅ xTTS audio playback completed")
        print("\n🎉 All tests passed! xTTS is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_xtts_manager():
    """Test the xTTS manager specifically."""
    print("\n🎛️ Testing xTTS Manager")
    print("=" * 40)
    
    try:
        from xtts_manager import XTTSManager
        
        print("1. Initializing xTTS Manager...")
        manager = XTTSManager(
            device_index=7,
            cache_enabled=False,
            model_name="tts_models/multilingual/multi-dataset/xtts_v2",
            language="en"
        )
        print("   ✅ xTTS Manager initialized")
        
        print("2. Testing synthesis and playback...")
        test_text = "This is a test of the xTTS manager class."
        print(f"   Text: '{test_text}'")
        
        manager.synthesize_and_play(test_text)
        print("   ✅ Manager test completed")
        
        # Clean up
        manager.stop()
        print("   ✅ Manager cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ xTTS Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 CoHost.AI - Quick xTTS Diagnostic")
    print("=" * 50)
    
    # Test basic xTTS functionality
    basic_success = test_basic_xtts()
    
    if basic_success:
        # Test the manager
        manager_success = test_xtts_manager()
        
        if manager_success:
            print("\n🎉 SUCCESS: xTTS is fully functional!")
            print("   You should have heard both a beep and synthesized speech.")
            print("   If you didn't hear anything, check:")
            print("   - Audio device configuration (device index 7)")
            print("   - System volume settings")
            print("   - Audio driver issues")
        else:
            print("\n⚠️ Basic xTTS works, but manager has issues")
    else:
        print("\n❌ Basic xTTS test failed")
        print("   Please check the error messages above")
    
    print("\n" + "=" * 50)
