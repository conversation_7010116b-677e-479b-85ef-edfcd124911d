Collecting TTS
  Using cached tts-0.22.0-cp39-cp39-win_amd64.whl
Collecting numpy==1.22.0 (from TTS)
  Using cached numpy-1.22.0-cp39-cp39-win_amd64.whl.metadata (2.1 kB)
Requirement already satisfied: cython>=0.29.30 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (3.1.2)
Requirement already satisfied: scipy>=1.11.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (1.13.1)
Requirement already satisfied: torch>=2.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (2.7.1)
Requirement already satisfied: torchaudio in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (2.7.1)
Requirement already satisfied: soundfile>=0.12.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (0.13.1)
Requirement already satisfied: librosa>=0.10.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (0.11.0)
Requirement already satisfied: scikit-learn>=1.3.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (1.6.1)
Requirement already satisfied: numba>=0.57.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (0.60.0)
Requirement already satisfied: inflect>=5.6.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (7.5.0)
Requirement already satisfied: tqdm>=4.64.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (4.67.1)
Requirement already satisfied: anyascii>=0.3.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (0.3.2)
Requirement already satisfied: pyyaml>=6.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (6.0.2)
Requirement already satisfied: fsspec>=2023.6.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (2025.5.1)
Requirement already satisfied: aiohttp>=3.8.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (3.12.11)
Requirement already satisfied: packaging>=23.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (25.0)
Collecting flask>=2.0.1 (from TTS)
  Using cached flask-3.1.1-py3-none-any.whl.metadata (3.0 kB)
Requirement already satisfied: pysbd>=0.3.4 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (0.3.4)
Collecting umap-learn>=0.5.1 (from TTS)
  Using cached umap_learn-0.5.7-py3-none-any.whl.metadata (21 kB)
Collecting pandas<2.0,>=1.4 (from TTS)
  Using cached pandas-1.5.3-cp39-cp39-win_amd64.whl.metadata (12 kB)
Requirement already satisfied: matplotlib>=3.7.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (3.9.4)
Collecting trainer>=0.0.32 (from TTS)
  Using cached trainer-0.0.36-py3-none-any.whl.metadata (8.1 kB)
Collecting coqpit>=0.0.16 (from TTS)
  Using cached coqpit-0.0.17-py3-none-any.whl.metadata (11 kB)
Collecting jieba (from TTS)
  Using cached jieba-0.42.1-py3-none-any.whl
Collecting pypinyin (from TTS)
  Using cached pypinyin-0.54.0-py2.py3-none-any.whl.metadata (12 kB)
Collecting hangul_romanize (from TTS)
  Using cached hangul_romanize-0.1.0-py3-none-any.whl.metadata (1.2 kB)
Collecting gruut==2.2.3 (from gruut[de,es,fr]==2.2.3->TTS)
  Using cached gruut-2.2.3-py3-none-any.whl
Collecting jamo (from TTS)
  Using cached jamo-0.4.1-py3-none-any.whl.metadata (2.3 kB)
Collecting nltk (from TTS)
  Using cached nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)
Collecting g2pkk>=0.1.1 (from TTS)
  Using cached g2pkk-0.1.2-py3-none-any.whl.metadata (2.0 kB)
Collecting bangla (from TTS)
  Using cached bangla-0.0.5-py3-none-any.whl.metadata (4.7 kB)
Collecting bnnumerizer (from TTS)
  Using cached bnnumerizer-0.0.2-py3-none-any.whl
Collecting bnunicodenormalizer (from TTS)
  Using cached bnunicodenormalizer-0.1.7-py3-none-any.whl.metadata (22 kB)
Requirement already satisfied: einops>=0.6.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (0.8.1)
Requirement already satisfied: transformers>=4.33.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (4.46.2)
Requirement already satisfied: encodec>=0.1.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (0.1.1)
Collecting unidecode>=1.3.2 (from TTS)
  Using cached Unidecode-1.4.0-py3-none-any.whl.metadata (13 kB)
Requirement already satisfied: num2words in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from TTS) (0.5.14)
Requirement already satisfied: spacy>=3 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy[ja]>=3->TTS) (3.7.5)
Requirement already satisfied: Babel<3.0.0,>=2.8.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (2.17.0)
Requirement already satisfied: dateparser~=1.1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (1.1.8)
Requirement already satisfied: gruut-ipa<1.0,>=0.12.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (0.13.0)
Requirement already satisfied: gruut_lang_en~=2.0.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (2.0.1)
Requirement already satisfied: jsonlines~=1.2.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (1.2.0)
Collecting networkx<3.0.0,>=2.5.0 (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS)
  Using cached networkx-2.8.8-py3-none-any.whl.metadata (5.1 kB)
Requirement already satisfied: python-crfsuite~=0.9.7 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (0.9.11)
Requirement already satisfied: gruut_lang_es~=2.0.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from gruut[de,es,fr]==2.2.3->TTS) (2.0.1)
Requirement already satisfied: gruut_lang_fr~=2.0.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from gruut[de,es,fr]==2.2.3->TTS) (2.0.2)
Requirement already satisfied: gruut_lang_de~=2.0.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from gruut[de,es,fr]==2.2.3->TTS) (2.0.1)
Requirement already satisfied: python-dateutil in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from dateparser~=1.1.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (2.9.0.post0)
Requirement already satisfied: pytz in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from dateparser~=1.1.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (2025.2)
Requirement already satisfied: regex!=2019.02.19,!=2021.8.27 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from dateparser~=1.1.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (2024.11.6)
Requirement already satisfied: tzlocal in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from dateparser~=1.1.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (5.3.1)
Requirement already satisfied: six in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from jsonlines~=1.2.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (1.17.0)
Requirement already satisfied: docopt>=0.6.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from num2words->TTS) (0.6.2)
Requirement already satisfied: aiohappyeyeballs>=2.5.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from aiohttp>=3.8.1->TTS) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from aiohttp>=3.8.1->TTS) (1.3.2)
Requirement already satisfied: async-timeout<6.0,>=4.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from aiohttp>=3.8.1->TTS) (5.0.1)
Requirement already satisfied: attrs>=17.3.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from aiohttp>=3.8.1->TTS) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from aiohttp>=3.8.1->TTS) (1.6.2)
Requirement already satisfied: multidict<7.0,>=4.5 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from aiohttp>=3.8.1->TTS) (6.4.4)
Requirement already satisfied: propcache>=0.2.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from aiohttp>=3.8.1->TTS) (0.3.1)
Requirement already satisfied: yarl<2.0,>=1.17.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from aiohttp>=3.8.1->TTS) (1.20.0)
Requirement already satisfied: typing-extensions>=4.1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from multidict<7.0,>=4.5->aiohttp>=3.8.1->TTS) (4.14.0)
Requirement already satisfied: idna>=2.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from yarl<2.0,>=1.17.0->aiohttp>=3.8.1->TTS) (3.10)
Collecting blinker>=1.9.0 (from flask>=2.0.1->TTS)
  Using cached blinker-1.9.0-py3-none-any.whl.metadata (1.6 kB)
Requirement already satisfied: click>=8.1.3 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from flask>=2.0.1->TTS) (8.1.8)
Requirement already satisfied: importlib-metadata>=3.6.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from flask>=2.0.1->TTS) (8.7.0)
Collecting itsdangerous>=2.2.0 (from flask>=2.0.1->TTS)
  Using cached itsdangerous-2.2.0-py3-none-any.whl.metadata (1.9 kB)
Requirement already satisfied: jinja2>=3.1.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from flask>=2.0.1->TTS) (3.1.6)
Requirement already satisfied: markupsafe>=2.1.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from flask>=2.0.1->TTS) (3.0.2)
Requirement already satisfied: werkzeug>=3.1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from flask>=2.0.1->TTS) (3.1.3)
Requirement already satisfied: colorama in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from click>=8.1.3->flask>=2.0.1->TTS) (0.4.6)
Requirement already satisfied: zipp>=3.20 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from importlib-metadata>=3.6.0->flask>=2.0.1->TTS) (3.23.0)
Requirement already satisfied: more_itertools>=8.5.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from inflect>=5.6.0->TTS) (10.7.0)
Requirement already satisfied: typeguard>=4.0.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from inflect>=5.6.0->TTS) (4.4.3)
Requirement already satisfied: audioread>=2.1.9 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from librosa>=0.10.0->TTS) (3.0.1)
INFO: pip is looking at multiple versions of librosa to determine which version is compatible with other requirements. This could take a while.
Collecting librosa>=0.10.0 (from TTS)
  Using cached librosa-0.10.2.post1-py3-none-any.whl.metadata (8.6 kB)
  Using cached librosa-0.10.2-py3-none-any.whl.metadata (8.6 kB)
  Using cached librosa-0.10.1-py3-none-any.whl.metadata (8.3 kB)
  Using cached librosa-0.10.0.post2-py3-none-any.whl.metadata (8.3 kB)
  Using cached librosa-0.10.0.post1-py3-none-any.whl.metadata (8.3 kB)
  Using cached librosa-0.10.0-py3-none-any.whl.metadata (8.3 kB)
Requirement already satisfied: joblib>=0.14 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from librosa>=0.10.0->TTS) (1.5.1)
Requirement already satisfied: decorator>=4.3.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from librosa>=0.10.0->TTS) (5.2.1)
Requirement already satisfied: pooch>=1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from librosa>=0.10.0->TTS) (1.8.2)
Requirement already satisfied: soxr>=0.3.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from librosa>=0.10.0->TTS) (0.5.0.post1)
Requirement already satisfied: lazy-loader>=0.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from librosa>=0.10.0->TTS) (0.4)
Requirement already satisfied: msgpack>=1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from librosa>=0.10.0->TTS) (1.1.0)
Requirement already satisfied: contourpy>=1.0.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from matplotlib>=3.7.0->TTS) (1.3.0)
Requirement already satisfied: cycler>=0.10 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from matplotlib>=3.7.0->TTS) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from matplotlib>=3.7.0->TTS) (4.58.2)
Requirement already satisfied: kiwisolver>=1.3.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from matplotlib>=3.7.0->TTS) (1.4.7)
INFO: pip is looking at multiple versions of matplotlib to determine which version is compatible with other requirements. This could take a while.
Collecting matplotlib>=3.7.0 (from TTS)
  Using cached matplotlib-3.9.3-cp39-cp39-win_amd64.whl.metadata (11 kB)
  Using cached matplotlib-3.9.2-cp39-cp39-win_amd64.whl.metadata (11 kB)
  Using cached matplotlib-3.9.1.post1-cp39-cp39-win_amd64.whl.metadata (11 kB)
  Using cached matplotlib-3.9.0-cp39-cp39-win_amd64.whl.metadata (11 kB)
  Using cached matplotlib-3.8.4-cp39-cp39-win_amd64.whl.metadata (5.9 kB)
Requirement already satisfied: pillow>=8 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from matplotlib>=3.7.0->TTS) (11.2.1)
Requirement already satisfied: pyparsing>=2.3.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from matplotlib>=3.7.0->TTS) (3.2.3)
Requirement already satisfied: importlib-resources>=3.2.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from matplotlib>=3.7.0->TTS) (6.5.2)
INFO: pip is looking at multiple versions of contourpy to determine which version is compatible with other requirements. This could take a while.
Collecting contourpy>=1.0.1 (from matplotlib>=3.7.0->TTS)
  Using cached contourpy-1.2.1-cp39-cp39-win_amd64.whl.metadata (5.8 kB)
Requirement already satisfied: llvmlite<0.44,>=0.43.0dev0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from numba>=0.57.0->TTS) (0.43.0)
Requirement already satisfied: platformdirs>=2.5.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from pooch>=1.0->librosa>=0.10.0->TTS) (4.3.8)
Requirement already satisfied: requests>=2.19.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from pooch>=1.0->librosa>=0.10.0->TTS) (2.32.4)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from requests>=2.19.0->pooch>=1.0->librosa>=0.10.0->TTS) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from requests>=2.19.0->pooch>=1.0->librosa>=0.10.0->TTS) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from requests>=2.19.0->pooch>=1.0->librosa>=0.10.0->TTS) (2025.4.26)
Requirement already satisfied: threadpoolctl>=3.1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from scikit-learn>=1.3.0->TTS) (3.6.0)
INFO: pip is looking at multiple versions of scipy to determine which version is compatible with other requirements. This could take a while.
Collecting scipy>=1.11.2 (from TTS)
  Using cached scipy-1.13.0-cp39-cp39-win_amd64.whl.metadata (60 kB)
  Using cached scipy-1.12.0-cp39-cp39-win_amd64.whl.metadata (60 kB)
  Using cached scipy-1.11.4-cp39-cp39-win_amd64.whl.metadata (60 kB)
Requirement already satisfied: cffi>=1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from soundfile>=0.12.0->TTS) (1.17.1)
Requirement already satisfied: pycparser in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from cffi>=1.0->soundfile>=0.12.0->TTS) (2.22)
Requirement already satisfied: spacy-legacy<3.1.0,>=3.0.11 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (3.0.12)
Requirement already satisfied: spacy-loggers<2.0.0,>=1.0.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (1.0.5)
Requirement already satisfied: murmurhash<1.1.0,>=0.28.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (1.0.13)
Requirement already satisfied: cymem<2.1.0,>=2.0.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (2.0.11)
Requirement already satisfied: preshed<3.1.0,>=3.0.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (3.0.10)
Requirement already satisfied: thinc<8.3.0,>=8.2.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (8.2.5)
Requirement already satisfied: wasabi<1.2.0,>=0.9.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (1.1.3)
Requirement already satisfied: srsly<3.0.0,>=2.4.3 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (2.5.1)
Requirement already satisfied: catalogue<2.1.0,>=2.0.6 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (2.0.10)
Requirement already satisfied: weasel<0.5.0,>=0.1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (0.4.1)
Requirement already satisfied: typer<1.0.0,>=0.3.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (0.16.0)
Requirement already satisfied: pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (2.11.5)
Requirement already satisfied: setuptools in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (58.1.0)
Requirement already satisfied: langcodes<4.0.0,>=3.2.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy>=3->spacy[ja]>=3->TTS) (3.5.0)
Requirement already satisfied: language-data>=1.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from langcodes<4.0.0,>=3.2.0->spacy>=3->spacy[ja]>=3->TTS) (1.3.0)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy>=3->spacy[ja]>=3->TTS) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy>=3->spacy[ja]>=3->TTS) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy>=3->spacy[ja]>=3->TTS) (0.4.1)
Requirement already satisfied: blis<0.8.0,>=0.7.8 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from thinc<8.3.0,>=8.2.2->spacy>=3->spacy[ja]>=3->TTS) (0.7.11)
Requirement already satisfied: confection<1.0.0,>=0.0.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from thinc<8.3.0,>=8.2.2->spacy>=3->spacy[ja]>=3->TTS) (0.1.5)
Requirement already satisfied: shellingham>=1.3.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS) (1.5.4)
Requirement already satisfied: rich>=10.11.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS) (13.9.2)
Requirement already satisfied: cloudpathlib<1.0.0,>=0.7.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from weasel<0.5.0,>=0.1.0->spacy>=3->spacy[ja]>=3->TTS) (0.21.1)
Requirement already satisfied: smart-open<8.0.0,>=5.2.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from weasel<0.5.0,>=0.1.0->spacy>=3->spacy[ja]>=3->TTS) (7.1.0)
Requirement already satisfied: wrapt in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from smart-open<8.0.0,>=5.2.1->weasel<0.5.0,>=0.1.0->spacy>=3->spacy[ja]>=3->TTS) (1.17.2)
Requirement already satisfied: marisa-trie>=1.1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from language-data>=1.2->langcodes<4.0.0,>=3.2.0->spacy>=3->spacy[ja]>=3->TTS) (1.2.1)
Requirement already satisfied: markdown-it-py>=2.2.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS) (2.19.1)
Requirement already satisfied: mdurl~=0.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS) (0.1.2)
Requirement already satisfied: sudachipy!=0.6.1,>=0.5.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy[ja]>=3->TTS) (0.6.10)
Requirement already satisfied: sudachidict-core>=20211220 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from spacy[ja]>=3->TTS) (20250515)
Requirement already satisfied: filelock in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from torch>=2.1->TTS) (3.18.0)
Requirement already satisfied: sympy>=1.13.3 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from torch>=2.1->TTS) (1.14.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from sympy>=1.13.3->torch>=2.1->TTS) (1.3.0)
Requirement already satisfied: psutil in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from trainer>=0.0.32->TTS) (7.0.0)
Requirement already satisfied: tensorboard in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from trainer>=0.0.32->TTS) (2.19.0)
Requirement already satisfied: huggingface-hub<1.0,>=0.23.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from transformers>=4.33.0->TTS) (0.32.4)
Requirement already satisfied: safetensors>=0.4.1 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from transformers>=4.33.0->TTS) (0.5.3)
Requirement already satisfied: tokenizers<0.21,>=0.20 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from transformers>=4.33.0->TTS) (0.20.3)
Collecting pynndescent>=0.5 (from umap-learn>=0.5.1->TTS)
  Using cached pynndescent-0.5.13-py3-none-any.whl.metadata (6.8 kB)
Requirement already satisfied: absl-py>=0.4 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from tensorboard->trainer>=0.0.32->TTS) (2.3.0)
Requirement already satisfied: grpcio>=1.48.2 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from tensorboard->trainer>=0.0.32->TTS) (1.73.0)
Requirement already satisfied: markdown>=2.6.8 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from tensorboard->trainer>=0.0.32->TTS) (3.8)
Requirement already satisfied: protobuf!=4.24.0,>=3.19.6 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from tensorboard->trainer>=0.0.32->TTS) (6.31.1)
Requirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from tensorboard->trainer>=0.0.32->TTS) (0.7.2)
Requirement already satisfied: tzdata in c:\users\<USER>\desktop\cohost.ai\venv\lib\site-packages (from tzlocal->dateparser~=1.1.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS) (2025.2)
Using cached numpy-1.22.0-cp39-cp39-win_amd64.whl (14.7 MB)
Using cached networkx-2.8.8-py3-none-any.whl (2.0 MB)
Using cached pandas-1.5.3-cp39-cp39-win_amd64.whl (10.9 MB)
Using cached coqpit-0.0.17-py3-none-any.whl (13 kB)
Using cached flask-3.1.1-py3-none-any.whl (103 kB)
Using cached blinker-1.9.0-py3-none-any.whl (8.5 kB)
Using cached g2pkk-0.1.2-py3-none-any.whl (25 kB)
Using cached itsdangerous-2.2.0-py3-none-any.whl (16 kB)
Using cached librosa-0.10.0-py3-none-any.whl (252 kB)
Using cached matplotlib-3.8.4-cp39-cp39-win_amd64.whl (7.7 MB)
Using cached contourpy-1.2.1-cp39-cp39-win_amd64.whl (182 kB)
Using cached scipy-1.11.4-cp39-cp39-win_amd64.whl (44.3 MB)
Using cached trainer-0.0.36-py3-none-any.whl (51 kB)
Using cached umap_learn-0.5.7-py3-none-any.whl (88 kB)
Using cached pynndescent-0.5.13-py3-none-any.whl (56 kB)
Using cached Unidecode-1.4.0-py3-none-any.whl (235 kB)
Using cached bangla-0.0.5-py3-none-any.whl (5.1 kB)
Using cached bnunicodenormalizer-0.1.7-py3-none-any.whl (23 kB)
Using cached hangul_romanize-0.1.0-py3-none-any.whl (4.6 kB)
Using cached jamo-0.4.1-py3-none-any.whl (9.5 kB)
Using cached nltk-3.9.1-py3-none-any.whl (1.5 MB)
Using cached pypinyin-0.54.0-py2.py3-none-any.whl (837 kB)
Installing collected packages: jieba, jamo, hangul_romanize, bnunicodenormalizer, bnnumerizer, bangla, unidecode, pypinyin, numpy, networkx, itsdangerous, coqpit, blinker, scipy, pandas, nltk, flask, contourpy, matplotlib, gruut, g2pkk, trainer, pynndescent, librosa, umap-learn, TTS
  Attempting uninstall: numpy
    Found existing installation: numpy 1.26.4
    Uninstalling numpy-1.26.4:
      Successfully uninstalled numpy-1.26.4
  Attempting uninstall: networkx
    Found existing installation: networkx 3.2.1
    Uninstalling networkx-3.2.1:
      Successfully uninstalled networkx-3.2.1
  Attempting uninstall: scipy
    Found existing installation: scipy 1.13.1
    Uninstalling scipy-1.13.1:
      Successfully uninstalled scipy-1.13.1
  Attempting uninstall: contourpy
    Found existing installation: contourpy 1.3.0
    Uninstalling contourpy-1.3.0:
      Successfully uninstalled contourpy-1.3.0
  Attempting uninstall: matplotlib
    Found existing installation: matplotlib 3.9.4
    Uninstalling matplotlib-3.9.4:
      Successfully uninstalled matplotlib-3.9.4
  Attempting uninstall: gruut
    Found existing installation: gruut 2.4.0
    Uninstalling gruut-2.4.0:
      Successfully uninstalled gruut-2.4.0
  Attempting uninstall: librosa
    Found existing installation: librosa 0.11.0
    Uninstalling librosa-0.11.0:
      Successfully uninstalled librosa-0.11.0

Successfully installed TTS-0.22.0 bangla-0.0.5 blinker-1.9.0 bnnumerizer-0.0.2 bnunicodenormalizer-0.1.7 contourpy-1.2.1 coqpit-0.0.17 flask-3.1.1 g2pkk-0.1.2 gruut-2.2.3 hangul_romanize-0.1.0 itsdangerous-2.2.0 jamo-0.4.1 jieba-0.42.1 librosa-0.10.0 matplotlib-3.8.4 networkx-2.8.8 nltk-3.9.1 numpy-1.22.0 pandas-1.5.3 pynndescent-0.5.13 pypinyin-0.54.0 scipy-1.11.4 trainer-0.0.36 umap-learn-0.5.7 unidecode-1.4.0
