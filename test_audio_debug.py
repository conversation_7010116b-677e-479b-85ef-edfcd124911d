"""
Audio debugging script for CoHost.AI xTTS implementation.

This script helps diagnose audio issues by testing different components step by step.

Author: <PERSON>: MIT
"""

import os
import sys
import time
import logging

# Add the src directory to the path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)
os.chdir(os.path.dirname(__file__))

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_audio_devices():
    """Test and list available audio devices."""
    print("🔊 Testing Audio Devices")
    print("-" * 40)
    
    try:
        import pyaudio
        
        p = pyaudio.PyAudio()
        device_count = p.get_device_count()
        
        print(f"Found {device_count} audio devices:")
        
        for i in range(device_count):
            try:
                info = p.get_device_info_by_index(i)
                device_type = "OUTPUT" if info['maxOutputChannels'] > 0 else "INPUT"
                if info['maxOutputChannels'] > 0:  # Only show output devices
                    print(f"  [{i:2d}] {info['name']} ({device_type}) - {info['maxOutputChannels']} channels")
            except Exception as e:
                print(f"  [{i:2d}] Error reading device info: {e}")
        
        p.terminate()
        
        # Check current configuration
        print(f"\nCurrent configuration uses device index: 7")
        try:
            p = pyaudio.PyAudio()
            info = p.get_device_info_by_index(7)
            print(f"Device 7: {info['name']} - {info['maxOutputChannels']} output channels")
            p.terminate()
        except Exception as e:
            print(f"❌ Error with device 7: {e}")
            return False
        
        return True
        
    except ImportError:
        print("❌ PyAudio not available")
        return False
    except Exception as e:
        print(f"❌ Audio device test failed: {e}")
        return False

def test_basic_audio_playback():
    """Test basic audio playback with a simple tone."""
    print("\n🎵 Testing Basic Audio Playback")
    print("-" * 40)
    
    try:
        import pyaudio
        import numpy as np
        
        # Generate a simple tone
        sample_rate = 22050
        duration = 2.0  # seconds
        frequency = 440  # A4 note
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        tone = np.sin(2 * np.pi * frequency * t) * 0.3  # 30% volume
        tone = (tone * 32767).astype(np.int16)
        
        # Play the tone
        p = pyaudio.PyAudio()
        
        stream = p.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=sample_rate,
            output=True,
            output_device_index=7,  # Use configured device
            frames_per_buffer=4096
        )
        
        print("Playing 2-second test tone on device 7...")
        print("🔊 You should hear a 440Hz tone now!")
        
        # Write audio data
        chunk_size = 4096
        for i in range(0, len(tone), chunk_size):
            chunk = tone[i:i+chunk_size]
            stream.write(chunk.tobytes())
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        print("✅ Basic audio playback test completed")
        return True
        
    except Exception as e:
        print(f"❌ Basic audio playback failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_xtts_import():
    """Test if xTTS can be imported and initialized."""
    print("\n🤖 Testing xTTS Import")
    print("-" * 40)
    
    try:
        from TTS.api import TTS
        import torch
        
        print("✅ TTS and torch imported successfully")
        
        # Check CUDA availability
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  CUDA not available, will use CPU (slower)")
        
        return True
        
    except ImportError as e:
        print(f"❌ xTTS import failed: {e}")
        print("   Please install coqui-tts: pip install coqui-tts")
        return False
    except Exception as e:
        print(f"❌ xTTS test failed: {e}")
        return False

def test_xtts_synthesis():
    """Test xTTS synthesis without full manager."""
    print("\n🎭 Testing xTTS Synthesis")
    print("-" * 40)
    
    try:
        from TTS.api import TTS
        import torch
        import numpy as np
        import pyaudio
        import wave
        import io
        
        print("Initializing xTTS model (this may take a moment)...")
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device)
        
        print("✅ xTTS model loaded successfully")
        
        test_text = "Hello! This is a test of the xTTS synthesis system."
        print(f"Synthesizing: '{test_text}'")
        
        # Generate audio
        wav = tts.tts(text=test_text, language="en")
        
        print(f"✅ Audio generated: {len(wav)} samples")
        
        # Convert to playable format
        if isinstance(wav, np.ndarray):
            if wav.dtype != np.int16:
                wav = (wav * 32767).astype(np.int16)
        
        # Create WAV in memory
        wav_io = io.BytesIO()
        with wave.open(wav_io, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(22050)
            wav_file.writeframes(wav.tobytes())
        
        wav_io.seek(0)
        audio_data = wav_io.getvalue()
        
        print(f"✅ WAV data created: {len(audio_data)} bytes")
        
        # Play the audio
        print("🔊 Playing synthesized audio...")
        
        audio_io = io.BytesIO(audio_data)
        wf = wave.open(audio_io, 'rb')
        
        p = pyaudio.PyAudio()
        stream = p.open(
            format=p.get_format_from_width(wf.getsampwidth()),
            channels=wf.getnchannels(),
            rate=wf.getframerate(),
            output=True,
            output_device_index=7,
            frames_per_buffer=4096
        )
        
        # Play audio
        data = wf.readframes(4096)
        while data:
            stream.write(data)
            data = wf.readframes(4096)
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        wf.close()
        
        print("✅ xTTS synthesis and playback test completed")
        return True
        
    except Exception as e:
        print(f"❌ xTTS synthesis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_xtts_manager():
    """Test the xTTS manager class."""
    print("\n🎛️ Testing xTTS Manager")
    print("-" * 40)
    
    try:
        from xtts_manager import XTTSManager
        
        print("Initializing xTTS Manager...")
        
        manager = XTTSManager(
            device_index=7,
            cache_enabled=False,
            model_name="tts_models/multilingual/multi-dataset/xtts_v2",
            language="en"
        )
        
        print("✅ xTTS Manager initialized")
        
        test_text = "This is a test of the xTTS manager class."
        print(f"Testing synthesis: '{test_text}'")
        
        manager.synthesize_and_play(test_text)
        
        print("✅ xTTS Manager test completed")
        
        # Clean up
        manager.stop()
        
        return True
        
    except Exception as e:
        print(f"❌ xTTS Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debugging function."""
    print("🔧 CoHost.AI Audio Debug Suite")
    print("=" * 50)
    
    tests = [
        ("Audio Devices", test_audio_devices),
        ("Basic Audio Playback", test_basic_audio_playback),
        ("xTTS Import", test_xtts_import),
        ("xTTS Synthesis", test_xtts_synthesis),
        ("xTTS Manager", test_xtts_manager),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            results[test_name] = test_func()
        except KeyboardInterrupt:
            print("\n⚠️ Test interrupted by user")
            break
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results[test_name] = False
        
        if test_name in results and not results[test_name]:
            print(f"\n⚠️ Test '{test_name}' failed. Continuing with remaining tests...")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Debug Results Summary:")
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    # Recommendations
    print("\n💡 Recommendations:")
    
    if "Audio Devices" in results and not results["Audio Devices"]:
        print("   - Check PyAudio installation")
        print("   - Verify audio drivers are working")
    
    if "Basic Audio Playback" in results and not results["Basic Audio Playback"]:
        print("   - Try a different audio device index")
        print("   - Check if device 7 is the correct output device")
        print("   - Verify audio is not muted")
    
    if "xTTS Import" in results and not results["xTTS Import"]:
        print("   - Install coqui-tts: pip install coqui-tts")
        print("   - Check Python version (3.9+ required)")
    
    if "xTTS Synthesis" in results and not results["xTTS Synthesis"]:
        print("   - Check internet connection (model download)")
        print("   - Verify sufficient disk space")
        print("   - Try running as administrator")
    
    if "xTTS Manager" in results and not results["xTTS Manager"]:
        print("   - Check the xTTS manager implementation")
        print("   - Verify all dependencies are correct")

if __name__ == "__main__":
    main()
