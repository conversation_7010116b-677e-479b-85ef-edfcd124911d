"""
Test script for xTTS functionality in CoHost.AI.

This script tests the xTTS manager independently to verify local TTS functionality.

Author: <PERSON>: MIT
"""

import os
import sys
import time
import logging

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from xtts_manager import XTTSManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_xtts() -> bool:
    """
    Test xTTS functionality independently.

    Performs a comprehensive test of the local text-to-speech system including:
    - xTTS model initialization
    - Audio device validation
    - Speech synthesis
    - Audio playback

    Returns:
        bool: True if all tests pass, False otherwise
    """
    # Configuration
    test_device_index = 7  # Recommended device from audio test
    test_text = "Hello! This is a test of the local xTTS system. Can you hear me clearly?"

    xtts_manager = None
    try:
        print("🔧 Initializing xTTS Manager...")
        print("   This may take a moment to download the model on first run...")
        
        xtts_manager = XTTSManager(
            device_index=test_device_index,
            cache_enabled=False,  # Disable cache for clean testing
            model_name="tts_models/multilingual/multi-dataset/xtts_v2",
            language="en"
        )
        print("✅ xTTS Manager initialized successfully")

        print("🎵 Testing xTTS synthesis and playback...")
        print(f"   Text: '{test_text}'")
        print(f"   Device: {test_device_index}")
        print("   Note: First synthesis may take longer as the model loads...")

        start_time = time.time()
        xtts_manager.synthesize_and_play(test_text)
        synthesis_time = time.time() - start_time

        print(f"✅ xTTS test completed successfully in {synthesis_time:.2f} seconds!")
        print("   If you heard the audio, local xTTS is working correctly.")
        
        # Test streaming synthesis
        print("\n🔄 Testing streaming synthesis...")
        
        def text_generator():
            sentences = [
                "This is a streaming test. ",
                "Each sentence should play as it's generated. ",
                "This reduces latency significantly. ",
                "Perfect for real-time responses!"
            ]
            for sentence in sentences:
                yield sentence
                time.sleep(0.5)  # Simulate streaming delay
        
        start_time = time.time()
        xtts_manager.stream_synthesize_and_play(text_generator())
        streaming_time = time.time() - start_time
        
        print(f"✅ Streaming test completed in {streaming_time:.2f} seconds!")
        print("   Streaming synthesis is working correctly.")
        
        return True

    except Exception as e:
        print(f"❌ ERROR: xTTS test failed: {e}")
        print("\n📋 Debug Information:")
        import traceback
        traceback.print_exc()
        
        print("\n🔧 Troubleshooting Tips:")
        print("1. Ensure coqui-tts is installed: pip install coqui-tts")
        print("2. Check if CUDA is available for faster inference")
        print("3. Verify audio device index is correct")
        print("4. First run may take time to download models")
        
        return False

    finally:
        # Clean up
        if xtts_manager:
            try:
                xtts_manager.stop()
                print("🧹 xTTS Manager cleaned up")
            except Exception as e:
                print(f"⚠️  Warning: Error during cleanup: {e}")

def test_xtts_with_voice_cloning() -> bool:
    """
    Test xTTS with voice cloning if a reference audio file is available.
    
    Returns:
        bool: True if test passes or is skipped, False if it fails
    """
    # Look for a reference audio file
    reference_files = [
        "reference_voice.wav",
        "speaker_reference.wav", 
        "voice_sample.wav"
    ]
    
    reference_wav = None
    for ref_file in reference_files:
        if os.path.exists(ref_file):
            reference_wav = ref_file
            break
    
    if not reference_wav:
        print("ℹ️  No reference audio file found, skipping voice cloning test")
        print("   To test voice cloning, place a WAV file named 'reference_voice.wav' in the current directory")
        return True
    
    print(f"\n🎭 Testing voice cloning with reference: {reference_wav}")
    
    xtts_manager = None
    try:
        xtts_manager = XTTSManager(
            device_index=7,
            cache_enabled=False,
            model_name="tts_models/multilingual/multi-dataset/xtts_v2",
            speaker_wav=reference_wav,
            language="en"
        )
        
        test_text = "This is a voice cloning test using your reference audio. How does it sound?"
        
        print("🎵 Synthesizing with voice cloning...")
        xtts_manager.synthesize_and_play(test_text)
        
        print("✅ Voice cloning test completed!")
        print("   Compare the output to your reference audio.")
        
        return True
        
    except Exception as e:
        print(f"❌ Voice cloning test failed: {e}")
        return False
        
    finally:
        if xtts_manager:
            try:
                xtts_manager.stop()
            except Exception:
                pass

def main():
    """Main test function."""
    print("🤖 CoHost.AI - xTTS Test Suite")
    print("=" * 50)
    
    # Test basic xTTS functionality
    print("\n📋 Test 1: Basic xTTS Functionality")
    basic_test_passed = test_xtts()
    
    # Test voice cloning if reference available
    print("\n📋 Test 2: Voice Cloning (Optional)")
    voice_cloning_passed = test_xtts_with_voice_cloning()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Basic xTTS: {'✅ PASSED' if basic_test_passed else '❌ FAILED'}")
    print(f"   Voice Cloning: {'✅ PASSED' if voice_cloning_passed else '❌ FAILED'}")
    
    if basic_test_passed:
        print("\n🎉 xTTS is ready for use in CoHost.AI!")
        print("   You can now enable USE_XTTS=true in your .env file")
    else:
        print("\n⚠️  xTTS setup needs attention before use")
        print("   Please resolve the issues above before enabling xTTS")
    
    return basic_test_passed and voice_cloning_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
