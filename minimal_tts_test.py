"""
Minimal TTS test to isolate issues.
"""

def test_imports():
    """Test if we can import required modules."""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"   CUDA available: {torch.cuda.is_available()}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import pyaudio
        print("✅ PyAudio imported")
    except ImportError as e:
        print(f"❌ PyAudio import failed: {e}")
        return False
    
    try:
        from TTS.api import TTS
        print("✅ TTS imported")
    except ImportError as e:
        print(f"❌ TTS import failed: {e}")
        print("   Try: pip uninstall coqui-tts && pip install TTS")
        return False
    
    return True

def test_audio_basic():
    """Test basic audio functionality."""
    print("\nTesting basic audio...")
    
    try:
        import pyaudio
        import numpy as np
        
        # List audio devices
        p = pyaudio.PyAudio()
        print("Available output devices:")
        for i in range(p.get_device_count()):
            try:
                info = p.get_device_info_by_index(i)
                if info['maxOutputChannels'] > 0:
                    print(f"  {i}: {info['name']}")
            except:
                pass
        
        # Test device 7
        try:
            info = p.get_device_info_by_index(7)
            print(f"\nUsing device 7: {info['name']}")
        except Exception as e:
            print(f"❌ Device 7 not available: {e}")
            p.terminate()
            return False
        
        # Generate and play a simple tone
        sample_rate = 22050
        duration = 0.5  # Short duration
        frequency = 440
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        tone = np.sin(2 * np.pi * frequency * t) * 0.1  # Low volume
        tone = (tone * 32767).astype(np.int16)
        
        stream = p.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=sample_rate,
            output=True,
            output_device_index=7
        )
        
        print("Playing test tone...")
        stream.write(tone.tobytes())
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        print("✅ Basic audio test completed")
        return True
        
    except Exception as e:
        print(f"❌ Audio test failed: {e}")
        return False

def test_tts_model():
    """Test TTS model loading."""
    print("\nTesting TTS model...")
    
    try:
        from TTS.api import TTS
        import torch
        
        print("Loading TTS model (this may take time on first run)...")
        
        # Use CPU to avoid potential CUDA issues
        device = "cpu"  # Force CPU for testing
        print(f"Using device: {device}")
        
        # Try to load the model
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device)
        print("✅ TTS model loaded successfully")
        
        # Test synthesis
        print("Testing synthesis...")
        text = "Hello world"
        wav = tts.tts(text=text, language="en")
        print(f"✅ Synthesis successful: {len(wav)} samples")
        
        return True
        
    except Exception as e:
        print(f"❌ TTS model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tts_playback():
    """Test TTS synthesis and playback."""
    print("\nTesting TTS playback...")
    
    try:
        from TTS.api import TTS
        import torch
        import pyaudio
        import numpy as np
        
        # Load model
        device = "cpu"
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2").to(device)
        
        # Synthesize
        text = "This is a test of text to speech."
        print(f"Synthesizing: '{text}'")
        wav = tts.tts(text=text, language="en")
        
        # Convert to playable format
        if wav.dtype != np.int16:
            wav = (wav * 32767).astype(np.int16)
        
        # Play
        p = pyaudio.PyAudio()
        stream = p.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=22050,
            output=True,
            output_device_index=7
        )
        
        print("Playing synthesized speech...")
        
        # Play in chunks to avoid buffer issues
        chunk_size = 4096
        for i in range(0, len(wav), chunk_size):
            chunk = wav[i:i+chunk_size]
            stream.write(chunk.tobytes())
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        print("✅ TTS playback completed")
        return True
        
    except Exception as e:
        print(f"❌ TTS playback failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Minimal TTS Test")
    print("=" * 30)
    
    # Run tests step by step
    if not test_imports():
        print("\n❌ Import test failed - fix imports first")
        exit(1)
    
    if not test_audio_basic():
        print("\n❌ Audio test failed - check audio setup")
        exit(1)
    
    if not test_tts_model():
        print("\n❌ TTS model test failed - check TTS installation")
        exit(1)
    
    if not test_tts_playback():
        print("\n❌ TTS playback failed - check audio/TTS integration")
        exit(1)
    
    print("\n🎉 All tests passed!")
    print("TTS should be working correctly.")
