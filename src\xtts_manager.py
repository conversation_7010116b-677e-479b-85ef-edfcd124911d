"""
xTTS Manager for CoHost.AI.

This module provides local text-to-speech functionality using coqui xTTS v2
with streaming capabilities, voice cloning, and optimized audio playback.

Author: <PERSON>
License: MIT
"""

import pyaudio
import wave
import os
import logging
import hashlib
import threading
import io
import time
import numpy as np
from typing import Optional, Dict, Generator, Union
from queue import Queue, Empty

try:
    from TTS.api import TTS
    import torch
except ImportError:
    raise ImportError(
        "coqui-tts package not found. "
        "Please install it with: pip install coqui-tts"
    )

from .OBSWebsocketsManager import OBSWebsocketsManager

logger = logging.getLogger(__name__)


class XTTSManager:
    """
    Local Text-to-Speech Manager using coqui xTTS v2.

    Provides high-performance local text-to-speech functionality with features including:
    - coqui xTTS v2 integration with voice cloning
    - Streaming TTS generation for reduced latency
    - Audio response caching for improved performance
    - Direct memory audio playback (no temporary files)
    - OBS character visibility integration
    - Configurable audio device selection
    - Thread-safe operations

    Attributes:
        device_index: PyAudio device index for audio output
        cache_enabled: Whether TTS response caching is enabled
        cache_size: Maximum number of cached audio responses
        buffer_size: Audio buffer size for playback optimization
        model_name: xTTS model name to use
        speaker_wav: Path to speaker reference audio file
        language: Language code for TTS
        tts_model: xTTS model instance
        obs_manager: OBS WebSocket manager for character visibility
        audio_cache: Cache for storing generated audio data
        pyaudio_instance: PyAudio instance for audio playback
        cache_lock: Thread lock for cache operations
        audio_queue: Queue for streaming audio playback
        playback_thread: Thread for audio playback
        is_playing: Flag to track playback state
    """

    def __init__(
        self,
        device_index: int = -1,
        cache_enabled: bool = True,
        cache_size: int = 50,
        buffer_size: int = 4096,
        model_name: str = "tts_models/multilingual/multi-dataset/xtts_v2",
        speaker_wav: Optional[str] = None,
        language: str = "en"
    ) -> None:
        """
        Initialize the xTTS Manager.

        Args:
            device_index: PyAudio device index for audio output (-1 for default)
            cache_enabled: Enable TTS response caching for repeated phrases
            cache_size: Maximum number of cached audio responses
            buffer_size: Audio buffer size for playback optimization
            model_name: xTTS model name to use
            speaker_wav: Path to speaker reference audio file for voice cloning
            language: Language code for TTS generation

        Raises:
            ImportError: If required dependencies are missing
            Exception: If initialization of any component fails
        """
        self.device_index: int = device_index
        self.cache_enabled: bool = cache_enabled
        self.cache_size: int = cache_size
        self.buffer_size: int = buffer_size
        self.model_name: str = model_name
        self.speaker_wav: Optional[str] = speaker_wav
        self.language: str = language

        # Initialize components
        self.tts_model: Optional[TTS] = None
        self.obs_manager: Optional[OBSWebsocketsManager] = None
        self.audio_cache: Dict[str, bytes] = {}
        self.pyaudio_instance: Optional[pyaudio.PyAudio] = None
        self.cache_lock: threading.Lock = threading.Lock()
        
        # Streaming audio components
        self.audio_queue: Queue = Queue()
        self.playback_thread: Optional[threading.Thread] = None
        self.is_playing: bool = False
        self.stop_playback: bool = False

        # Initialize all components
        self._initialize_model()
        self._initialize_obs()
        self._initialize_audio()
        self._start_playback_thread()

        logger.info("xTTS Manager initialized successfully")

    def _initialize_model(self):
        """Initialize xTTS model."""
        try:
            logger.info(f"Loading xTTS model: {self.model_name}")
            
            # Check if CUDA is available for faster inference
            device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info(f"Using device: {device}")
            
            self.tts_model = TTS(self.model_name).to(device)
            
            # Validate speaker reference file if provided
            if self.speaker_wav and not os.path.exists(self.speaker_wav):
                logger.warning(f"Speaker reference file not found: {self.speaker_wav}")
                self.speaker_wav = None
            
            logger.info("xTTS model initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize xTTS model: {e}")
            raise

    def _initialize_obs(self):
        """Initialize OBS WebSocket manager."""
        try:
            self.obs_manager = OBSWebsocketsManager()
            logger.info("OBS WebSocket manager initialized for xTTS")
        except Exception as e:
            logger.warning(f"Failed to initialize OBS manager: {e}")
            # Don't raise here - TTS can work without OBS

    def _initialize_audio(self):
        """Initialize PyAudio for audio playback."""
        try:
            self.pyaudio_instance = pyaudio.PyAudio()
            
            # Log available audio devices for debugging
            if logger.isEnabledFor(logging.DEBUG):
                device_count = self.pyaudio_instance.get_device_count()
                logger.debug(f"Available audio devices: {device_count}")
                for i in range(device_count):
                    info = self.pyaudio_instance.get_device_info_by_index(i)
                    logger.debug(f"Device {i}: {info['name']}")
            
            logger.info("PyAudio initialized for xTTS playback")
        except Exception as e:
            logger.error(f"Failed to initialize PyAudio: {e}")
            raise

    def _start_playback_thread(self):
        """Start the audio playback thread."""
        self.playback_thread = threading.Thread(
            target=self._audio_playback_worker,
            daemon=True,
            name="xTTS-AudioPlayback"
        )
        self.playback_thread.start()
        logger.debug("Audio playback thread started")

    def _audio_playback_worker(self):
        """Worker thread for continuous audio playback."""
        while not self.stop_playback:
            try:
                # Wait for audio data with timeout
                audio_data = self.audio_queue.get(timeout=1.0)
                if audio_data is None:  # Shutdown signal
                    break
                
                self.is_playing = True
                self._play_audio_data(audio_data)
                self.is_playing = False
                self.audio_queue.task_done()
                
            except Empty:
                continue  # Check shutdown flag
            except Exception as e:
                logger.error(f"Error in audio playback worker: {e}")
                if not self.audio_queue.empty():
                    self.audio_queue.task_done()

    def _play_audio_data(self, audio_data: bytes):
        """
        Play audio data directly from memory.

        Args:
            audio_data: Raw audio data to play
        """
        if not self.pyaudio_instance:
            logger.error("PyAudio not initialized")
            return

        try:
            # Parse the WAV data directly from memory
            audio_io = io.BytesIO(audio_data)
            wf = wave.open(audio_io, 'rb')

            # Open stream with optimized buffer size
            stream = self.pyaudio_instance.open(
                format=self.pyaudio_instance.get_format_from_width(wf.getsampwidth()),
                channels=wf.getnchannels(),
                rate=wf.getframerate(),
                output=True,
                output_device_index=self.device_index,
                frames_per_buffer=self.buffer_size
            )

            logger.debug(f"Playing audio on device {self.device_index}: "
                        f"{wf.getnchannels()} channels, {wf.getframerate()} Hz")

            # Read and play data in optimized chunks
            data = wf.readframes(self.buffer_size)
            while data and not self.stop_playback:
                stream.write(data)
                data = wf.readframes(self.buffer_size)

            # Clean up
            stream.stop_stream()
            stream.close()
            wf.close()
            
            logger.debug("Audio playback completed")

        except Exception as e:
            logger.error(f"Error playing audio: {e}")

    def _get_cache_key(self, text: str) -> str:
        """
        Generate cache key for text.

        Args:
            text: Text to generate cache key for

        Returns:
            MD5 hash of the text as cache key
        """
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _get_cached_audio(self, text: str) -> Optional[bytes]:
        """
        Retrieve cached audio data for text.

        Args:
            text: Text to retrieve cached audio for

        Returns:
            Cached audio data or None if not found
        """
        if not self.cache_enabled:
            return None

        cache_key = self._get_cache_key(text)
        with self.cache_lock:
            return self.audio_cache.get(cache_key)

    def _cache_audio(self, text: str, audio_data: bytes):
        """
        Cache audio data for text.

        Args:
            text: Text to cache audio for
            audio_data: Audio data to cache
        """
        if not self.cache_enabled:
            return

        cache_key = self._get_cache_key(text)
        with self.cache_lock:
            # Implement LRU cache behavior
            if len(self.audio_cache) >= self.cache_size:
                # Remove oldest entry
                oldest_key = next(iter(self.audio_cache))
                del self.audio_cache[oldest_key]
                logger.debug(f"Removed oldest cache entry: {oldest_key}")

            self.audio_cache[cache_key] = audio_data
            logger.debug(f"Cached audio for text: {text[:50]}...")

    def synthesize_to_bytes(self, text: str) -> bytes:
        """
        Synthesize text to audio bytes using xTTS.

        Args:
            text: Text to synthesize

        Returns:
            Audio data as bytes in WAV format

        Raises:
            Exception: If synthesis fails
        """
        if not self.tts_model:
            raise Exception("xTTS model not initialized")

        try:
            logger.debug(f"Synthesizing text with xTTS: {text[:50]}...")

            # Generate audio using xTTS
            if self.speaker_wav:
                # Use voice cloning with reference audio
                wav = self.tts_model.tts(
                    text=text,
                    speaker_wav=self.speaker_wav,
                    language=self.language
                )
            else:
                # Use default voice
                wav = self.tts_model.tts(
                    text=text,
                    language=self.language
                )

            # Convert numpy array to WAV bytes
            audio_data = self._numpy_to_wav_bytes(wav)
            logger.debug(f"xTTS synthesis completed, audio size: {len(audio_data)} bytes")

            return audio_data

        except Exception as e:
            logger.error(f"xTTS synthesis failed: {e}")
            raise

    def _numpy_to_wav_bytes(self, wav_array: np.ndarray, sample_rate: int = 22050) -> bytes:
        """
        Convert numpy array to WAV bytes.

        Args:
            wav_array: Audio data as numpy array
            sample_rate: Sample rate for the audio

        Returns:
            Audio data as WAV bytes
        """
        # Ensure the array is in the correct format
        if wav_array.dtype != np.int16:
            # Convert float to int16
            wav_array = (wav_array * 32767).astype(np.int16)

        # Create WAV file in memory
        wav_io = io.BytesIO()
        with wave.open(wav_io, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(wav_array.tobytes())

        wav_io.seek(0)
        return wav_io.getvalue()

    def synthesize_and_play(self, text: str, scene_name: str = "In-Game [OLD]",
                           bot_source: str = "AIBot", top_source: str = "AITop"):
        """
        Synthesize text to speech and play it with OBS integration.
        Uses caching and parallel processing for better performance.

        Args:
            text: Text to synthesize
            scene_name: OBS scene name for character visibility
            bot_source: OBS source name for bot character
            top_source: OBS source name for top character
        """
        if not self.tts_model:
            logger.error("xTTS model not initialized")
            return

        try:
            # Check cache first
            audio_data = self._get_cached_audio(text)

            if audio_data:
                logger.debug(f"Using cached audio for: {text[:50]}...")
            else:
                logger.debug(f"Synthesizing new audio with xTTS: {text[:50]}...")
                audio_data = self.synthesize_to_bytes(text)
                self._cache_audio(text, audio_data)

            # Handle OBS visibility in parallel
            if self.obs_manager:
                obs_thread = threading.Thread(
                    target=self._handle_obs_visibility,
                    args=(scene_name, bot_source, top_source, True),
                    daemon=True,
                    name="xTTS-OBS-Show"
                )
                obs_thread.start()

            # Queue audio for playback
            self.audio_queue.put(audio_data)

            # Wait for playback to start, then handle OBS hide
            if self.obs_manager:
                # Wait a bit for audio to start playing
                time.sleep(0.1)

                # Calculate approximate audio duration for OBS timing
                audio_duration = self._estimate_audio_duration(audio_data)

                hide_thread = threading.Thread(
                    target=self._delayed_obs_hide,
                    args=(scene_name, bot_source, top_source, audio_duration),
                    daemon=True,
                    name="xTTS-OBS-Hide"
                )
                hide_thread.start()

            logger.info(f"Queued xTTS audio for playback: {text[:50]}...")

        except Exception as e:
            logger.error(f"Error in xTTS synthesize_and_play: {e}")

    def _estimate_audio_duration(self, audio_data: bytes) -> float:
        """
        Estimate audio duration from WAV data.

        Args:
            audio_data: WAV audio data

        Returns:
            Estimated duration in seconds
        """
        try:
            audio_io = io.BytesIO(audio_data)
            with wave.open(audio_io, 'rb') as wf:
                frames = wf.getnframes()
                rate = wf.getframerate()
                duration = frames / float(rate)
                return duration
        except Exception as e:
            logger.warning(f"Could not estimate audio duration: {e}")
            return 3.0  # Default fallback duration

    def _delayed_obs_hide(self, scene_name: str, bot_source: str, top_source: str, delay: float):
        """
        Hide OBS sources after a delay.

        Args:
            scene_name: OBS scene name
            bot_source: OBS bot source name
            top_source: OBS top source name
            delay: Delay in seconds before hiding
        """
        time.sleep(delay)
        self._handle_obs_visibility(scene_name, bot_source, top_source, False)

    def _handle_obs_visibility(self, scene_name: str, bot_source: str, top_source: str, visible: bool):
        """Handle OBS visibility changes in a separate thread."""
        try:
            if self.obs_manager:
                self.obs_manager.set_source_visibility(scene_name, bot_source, visible)
                self.obs_manager.set_source_visibility(scene_name, top_source, visible)
        except Exception as e:
            logger.warning(f"Failed to set OBS visibility: {e}")

    def stream_synthesize_and_play(self, text_stream: Generator[str, None, None],
                                  scene_name: str = "In-Game [OLD]",
                                  bot_source: str = "AIBot", top_source: str = "AITop"):
        """
        Synthesize and play streaming text with minimal latency.

        This method processes text chunks as they arrive from a streaming LLM,
        synthesizing and playing audio with minimal delay.

        Args:
            text_stream: Generator yielding text chunks
            scene_name: OBS scene name for character visibility
            bot_source: OBS source name for bot character
            top_source: OBS source name for top character
        """
        if not self.tts_model:
            logger.error("xTTS model not initialized")
            return

        try:
            # Show OBS sources at start
            if self.obs_manager:
                obs_thread = threading.Thread(
                    target=self._handle_obs_visibility,
                    args=(scene_name, bot_source, top_source, True),
                    daemon=True,
                    name="xTTS-Stream-OBS-Show"
                )
                obs_thread.start()

            sentence_buffer = ""
            total_audio_duration = 0.0

            for text_chunk in text_stream:
                sentence_buffer += text_chunk

                # Check if we have a complete sentence or significant chunk
                if self._is_sentence_complete(sentence_buffer):
                    # Synthesize the complete sentence
                    audio_data = self.synthesize_to_bytes(sentence_buffer.strip())

                    # Queue for playback
                    self.audio_queue.put(audio_data)

                    # Track total duration
                    total_audio_duration += self._estimate_audio_duration(audio_data)

                    logger.debug(f"Streamed sentence: {sentence_buffer.strip()[:50]}...")
                    sentence_buffer = ""

            # Handle any remaining text
            if sentence_buffer.strip():
                audio_data = self.synthesize_to_bytes(sentence_buffer.strip())
                self.audio_queue.put(audio_data)
                total_audio_duration += self._estimate_audio_duration(audio_data)
                logger.debug(f"Final sentence: {sentence_buffer.strip()[:50]}...")

            # Hide OBS sources after all audio finishes
            if self.obs_manager:
                hide_thread = threading.Thread(
                    target=self._delayed_obs_hide,
                    args=(scene_name, bot_source, top_source, total_audio_duration + 0.5),
                    daemon=True,
                    name="xTTS-Stream-OBS-Hide"
                )
                hide_thread.start()

            logger.info("Streaming TTS synthesis completed")

        except Exception as e:
            logger.error(f"Error in streaming TTS: {e}")

    def _is_sentence_complete(self, text: str) -> bool:
        """
        Check if text contains a complete sentence.

        Args:
            text: Text to check

        Returns:
            True if text contains a complete sentence
        """
        # Simple sentence boundary detection
        sentence_endings = ['.', '!', '?', ';']
        return any(ending in text for ending in sentence_endings) and len(text.strip()) > 10

    def stop(self):
        """Stop the xTTS manager and clean up resources."""
        logger.info("Stopping xTTS Manager...")

        # Stop playback thread
        self.stop_playback = True
        self.audio_queue.put(None)  # Shutdown signal

        if self.playback_thread and self.playback_thread.is_alive():
            self.playback_thread.join(timeout=2.0)

        # Clean up PyAudio
        if self.pyaudio_instance:
            try:
                self.pyaudio_instance.terminate()
            except Exception as e:
                logger.warning(f"Error terminating PyAudio: {e}")

        logger.info("xTTS Manager stopped")

    def __del__(self):
        """Cleanup when object is destroyed."""
        try:
            self.stop()
        except Exception:
            pass  # Ignore errors during cleanup
