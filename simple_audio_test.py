"""Simple audio test for debugging."""

import sys
import os
sys.path.append('src')

def test_audio_devices():
    try:
        import pyaudio
        p = pyaudio.PyAudio()
        print("Available audio output devices:")
        for i in range(p.get_device_count()):
            info = p.get_device_info_by_index(i)
            if info['maxOutputChannels'] > 0:
                print(f"  {i}: {info['name']} ({info['maxOutputChannels']} channels)")
        p.terminate()
        return True
    except Exception as e:
        print(f"Audio test failed: {e}")
        return False

def test_basic_tone():
    try:
        import pyaudio
        import numpy as np
        
        # Generate a simple tone
        sample_rate = 22050
        duration = 1.0
        frequency = 440
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        tone = np.sin(2 * np.pi * frequency * t) * 0.3
        tone = (tone * 32767).astype(np.int16)
        
        p = pyaudio.PyAudio()
        stream = p.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=sample_rate,
            output=True,
            output_device_index=7,
            frames_per_buffer=4096
        )
        
        print("Playing test tone on device 7...")
        stream.write(tone.tobytes())
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        print("Tone playback completed")
        return True
        
    except Exception as e:
        print(f"Tone test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_xtts_import():
    try:
        from TTS.api import TTS
        import torch
        print("xTTS imports successful")
        print(f"CUDA available: {torch.cuda.is_available()}")
        return True
    except Exception as e:
        print(f"xTTS import failed: {e}")
        return False

if __name__ == "__main__":
    print("=== Simple Audio Test ===")
    
    print("\n1. Testing audio devices...")
    test_audio_devices()
    
    print("\n2. Testing basic tone playback...")
    test_basic_tone()
    
    print("\n3. Testing xTTS imports...")
    test_xtts_import()
    
    print("\nTest completed!")
